import React from 'react';

const ResultDisplay = ({ result, onReset }) => {
  if (!result) return null;

  const { diagnosis, confidence, recommendations, riskLevel } = result;

  const getRiskLevelColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-white rounded-lg shadow-lg border">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-0">
          Diagnostic Results
        </h2>
        <button
          onClick={onReset}
          className="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors self-start sm:self-auto"
        >
          New Analysis
        </button>
      </div>

      {/* Diagnosis */}
      <div className="mb-4 sm:mb-6">
        <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2">
          Primary Diagnosis
        </h3>
        <p className="text-gray-700 text-sm sm:text-base">
          {diagnosis || 'No specific diagnosis provided'}
        </p>
      </div>

      {/* Confidence and Risk Level */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
        {confidence && (
          <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
            <h4 className="text-xs sm:text-sm font-medium text-gray-600 mb-1">
              Confidence Level
            </h4>
            <p className={`text-xl sm:text-2xl font-bold ${getConfidenceColor(confidence)}`}>
              {confidence}%
            </p>
          </div>
        )}

        {riskLevel && (
          <div className="p-3 sm:p-4 bg-gray-50 rounded-lg">
            <h4 className="text-xs sm:text-sm font-medium text-gray-600 mb-1">
              Risk Level
            </h4>
            <span className={`inline-block px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium border ${getRiskLevelColor(riskLevel)}`}>
              {riskLevel}
            </span>
          </div>
        )}
      </div>

      {/* Recommendations */}
      {recommendations && recommendations.length > 0 && (
        <div className="mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-3">
            Recommendations
          </h3>
          <ul className="space-y-2 sm:space-y-3">
            {recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start">
                <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 text-sm sm:text-base">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Disclaimer */}
      <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-xs sm:text-sm text-yellow-800">
          <strong>Medical Disclaimer:</strong> This analysis is for informational purposes only and should not replace professional medical advice. Please consult with a healthcare provider for proper diagnosis and treatment.
        </p>
      </div>
    </div>
  );
};

export default ResultDisplay;
