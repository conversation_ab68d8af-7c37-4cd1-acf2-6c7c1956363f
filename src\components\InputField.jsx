import React from 'react';

const InputField = ({ 
  label, 
  name, 
  type = 'text', 
  placeholder, 
  register, 
  error, 
  required = false,
  className = '' 
}) => {
  return (
    <div className="mb-3 sm:mb-4">
      <label
        htmlFor={name}
        className="block text-sm sm:text-base font-medium text-gray-700 mb-1 sm:mb-2"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <input
        id={name}
        type={type}
        placeholder={placeholder}
        {...register(name)}
        className={`
          w-full px-3 py-2 sm:py-3 border border-gray-300 rounded-md shadow-sm text-sm sm:text-base
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          ${className}
        `}
      />
      {error && (
        <p className="mt-1 text-xs sm:text-sm text-red-600">
          {error.message}
        </p>
      )}
    </div>
  );
};

export default InputField;
