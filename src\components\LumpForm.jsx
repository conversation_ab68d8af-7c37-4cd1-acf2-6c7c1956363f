import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import InputField from './InputField';
import SelectField from './SelectField';
import LoadingSpinner from './LoadingSpinner';
import ResultDisplay from './ResultDisplay';
import { submitDiagnosisData } from '../services/diagnosisApi';

const LumpForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [apiError, setApiError] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset
  } = useForm({
    mode: 'onChange'
  });

  // Form field options
  const hardnessOptions = [
    { value: 'soft', label: 'Soft' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' }
  ];

  const painSeverityOptions = [
    { value: 'none', label: 'None' },
    { value: 'mild', label: 'Mild' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'severe', label: 'Severe' }
  ];

  const positionOptions = [
    { value: 'top', label: 'Top' },
    { value: 'bottom', label: 'Bottom' },
    { value: 'nipple_area', label: 'Nipple Area' }
  ];

  const onSubmit = async (data) => {
    setIsLoading(true);
    setApiError(null);
    
    try {
      const response = await submitDiagnosisData(data);
      setResult(response);
    } catch (error) {
      setApiError(error.message || 'An error occurred while processing your request.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    reset();
    setResult(null);
    setApiError(null);
  };

  if (result) {
    return <ResultDisplay result={result} onReset={handleReset} />;
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-4 sm:p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
          Medical Lump Diagnosis
        </h1>
        <p className="text-sm sm:text-base text-gray-600">
          Please provide the following information for an AI-assisted preliminary assessment.
        </p>
      </div>

      {apiError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">
            <strong>Error:</strong> {apiError}
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
        {/* Name Field */}
        <InputField
          label="Full Name"
          name="name"
          placeholder="Enter your full name"
          register={(name) => register(name, {
            required: 'Name is required',
            minLength: {
              value: 2,
              message: 'Name must be at least 2 characters long'
            },
            pattern: {
              value: /^[a-zA-Z\s]+$/,
              message: 'Name can only contain letters and spaces'
            }
          })}
          error={errors.name}
          required
        />

        {/* Age Field */}
        <InputField
          label="Age"
          name="age"
          type="number"
          placeholder="Enter your age"
          register={(name) => register(name, {
            required: 'Age is required',
            min: {
              value: 1,
              message: 'Age must be at least 1 year'
            },
            max: {
              value: 120,
              message: 'Age must be less than 120 years'
            },
            valueAsNumber: true
          })}
          error={errors.age}
          required
        />

        {/* Hardness of Lump */}
        <SelectField
          label="Hardness of Lump"
          name="hardness"
          options={hardnessOptions}
          placeholder="Select hardness level"
          register={(name) => register(name, {
            required: 'Please select the hardness of the lump'
          })}
          error={errors.hardness}
          required
        />

        {/* Severity of Pain */}
        <SelectField
          label="Severity of Pain"
          name="painSeverity"
          options={painSeverityOptions}
          placeholder="Select pain severity"
          register={(name) => register(name, {
            required: 'Please select the severity of pain'
          })}
          error={errors.painSeverity}
          required
        />

        {/* Position of Lump */}
        <SelectField
          label="Position of Lump"
          name="position"
          options={positionOptions}
          placeholder="Select lump position"
          register={(name) => register(name, {
            required: 'Please select the position of the lump'
          })}
          error={errors.position}
          required
        />

        {/* Duration of Pain */}
        <InputField
          label="Duration of Pain"
          name="duration"
          placeholder="e.g., 2 days, 1 week, 3 months"
          register={(name) => register(name, {
            required: 'Duration of pain is required',
            minLength: {
              value: 2,
              message: 'Please provide a more detailed duration'
            }
          })}
          error={errors.duration}
          required
        />

        {/* Submit Button */}
        <div className="pt-4 sm:pt-6">
          <button
            type="submit"
            disabled={!isValid || isLoading}
            className={`
              w-full py-3 sm:py-4 px-4 rounded-md font-medium text-white transition-colors text-sm sm:text-base
              ${!isValid || isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
              }
            `}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <LoadingSpinner size="small" text="" />
                <span className="ml-2">Analyzing...</span>
              </div>
            ) : (
              'Get Diagnosis'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LumpForm;
