import React from 'react';

const SelectField = ({ 
  label, 
  name, 
  options, 
  placeholder = 'Select an option', 
  register, 
  error, 
  required = false,
  className = '' 
}) => {
  return (
    <div className="mb-3 sm:mb-4">
      <label
        htmlFor={name}
        className="block text-sm sm:text-base font-medium text-gray-700 mb-1 sm:mb-2"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <select
        id={name}
        {...register(name)}
        className={`
          w-full px-3 py-2 sm:py-3 border border-gray-300 rounded-md shadow-sm text-sm sm:text-base
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          bg-white
          ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          ${className}
        `}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <p className="mt-1 text-xs sm:text-sm text-red-600">
          {error.message}
        </p>
      )}
    </div>
  );
};

export default SelectField;
