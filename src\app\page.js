'use client';

import LumpForm from '../components/LumpForm';
import ErrorBoundary from '../components/ErrorBoundary';

export default function Home() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Medical Lump Diagnosis Assistant
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Get an AI-powered preliminary assessment of your symptoms. This tool provides
              educational information and should not replace professional medical advice.
            </p>
          </div>

          <LumpForm />

          <div className="mt-12 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">
                Important Notice
              </h2>
              <p className="text-blue-800 text-sm">
                This diagnostic tool is for educational purposes only. Always consult with
                qualified healthcare professionals for proper medical diagnosis and treatment.
                If you have concerning symptoms, seek immediate medical attention.
              </p>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}
