# Medical Lump Diagnosis Assistant

A comprehensive React + Tailwind CSS frontend application for medical lump diagnosis input interface with AI-powered diagnostic predictions.

## 🏥 Project Overview

This application provides a user-friendly interface for collecting medical information about lumps and provides AI-assisted preliminary assessments. The tool is designed for educational purposes and should not replace professional medical advice.

## ✨ Features

- **Comprehensive Form Interface**: 6 required medical input fields with validation
- **Mobile-Responsive Design**: Optimized for all screen sizes using Tailwind CSS
- **AI Integration**: Simulated AI diagnostic API with realistic responses
- **Form Validation**: Real-time validation with react-hook-form
- **Loading States**: Professional loading indicators during API calls
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Accessibility**: WCAG-compliant form design with proper labels and ARIA attributes

## 🛠️ Technical Stack

- **Frontend Framework**: Next.js 15.4.5 with React 19.1.0
- **Styling**: Tailwind CSS 4.0 with mobile-first responsive design
- **Form Management**: react-hook-form for validation and state management
- **HTTP Client**: Axios for API communication
- **Development**: ESLint for code quality

## 📋 Required Input Fields

1. **Name** - Text input (required, 2+ characters, letters and spaces only)
2. **Age** - Number input (required, range 1-120 years)
3. **Hardness of Lump** - Select dropdown (Soft, Medium, Hard)
4. **Severity of Pain** - Select dropdown (None, Mild, Moderate, Severe)
5. **Position of Lump** - Select dropdown (Top, Bottom, Nipple Area)
6. **Duration of Pain** - Text input (required, descriptive duration)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd diagnosis
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser
